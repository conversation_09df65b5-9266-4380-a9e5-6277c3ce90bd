"""
Simple Chat Service - Clean and minimal chat implementation
"""
import uuid
from datetime import datetime
from typing import Dict, Any, List
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.messages import HumanMessage, AIMessage
from langchain.tools import Tool
from bson import ObjectId

from src.models.message import MessageService, MessageCreate, MessageType
from src.config.database import get_collection_sync, COLLECTIONS


class SimpleChatService:
    """Simple chat service with agent and tools"""
    
    def __init__(self):
        # Initialize message service
        self.message_service = MessageService(get_collection_sync(COLLECTIONS['messages']))
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.7
        )
        
        # Setup tools
        self.tools = self._setup_tools()
        
        # Setup agent
        self.agent = self._setup_agent()
    
    def _setup_tools(self) -> List[Tool]:
        """Setup tools for the agent"""
        
        def get_current_time() -> str:
            """Get the current time"""
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        def calculate(expression: str) -> str:
            """Calculate a mathematical expression"""
            try:
                # Simple calculator - be careful with eval in production
                result = eval(expression.replace("^", "**"))
                return str(result)
            except Exception as e:
                return f"Error calculating: {str(e)}"
        
        def search_messages(query: str) -> str:
            """Search previous messages"""
            try:
                # Simple search in recent messages
                messages = self.message_service.collection.find(
                    {"content": {"$regex": query, "$options": "i"}}
                ).sort("timestamp", -1).limit(5)
                
                results = []
                for msg in messages:
                    results.append(f"- {msg['content'][:100]}...")
                
                return "\n".join(results) if results else "No messages found"
            except Exception as e:
                return f"Error searching: {str(e)}"
        
        return [
            Tool(
                name="get_current_time",
                description="Get the current date and time",
                func=get_current_time
            ),
            Tool(
                name="calculate",
                description="Calculate mathematical expressions. Use this for any math calculations.",
                func=calculate
            ),
            Tool(
                name="search_messages",
                description="Search through previous messages for relevant information",
                func=search_messages
            )
        ]
    
    def _setup_agent(self) -> AgentExecutor:
        """Setup the agent with tools"""
        
        prompt = PromptTemplate.from_template("""
You are a helpful AI assistant. You have access to tools to help answer questions.

Available tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {input}
Thought: {agent_scratchpad}
""")
        
        agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3
        )
    
    async def chat(self, message: str, session_id: str = None, user_id: str = None) -> Dict[str, Any]:
        """Process a chat message and return response"""
        
        # Generate session_id if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
        
        # Generate user_id if not provided
        if not user_id:
            user_id = "anonymous"
        
        start_time = datetime.now()
        
        try:
            # Store user message
            user_message_id = ObjectId()
            user_message_doc = {
                "_id": user_message_id,
                "content": message,
                "message_type": MessageType.USER,
                "session_id": session_id,
                "user_id": user_id,
                "timestamp": datetime.now(),
                "metadata": {}
            }
            
            self.message_service.collection.insert_one(user_message_doc)
            
            # Process with agent
            response = self.agent.invoke({"input": message})
            assistant_response = response.get("output", "I'm sorry, I couldn't process your request.")
            
            # Calculate response time
            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Store assistant message
            assistant_message_id = ObjectId()
            assistant_message_doc = {
                "_id": assistant_message_id,
                "content": assistant_response,
                "message_type": MessageType.ASSISTANT,
                "session_id": session_id,
                "user_id": user_id,
                "timestamp": datetime.now(),
                "metadata": {
                    "response_time_ms": response_time_ms
                }
            }
            
            self.message_service.collection.insert_one(assistant_message_doc)
            
            return {
                "response": assistant_response,
                "session_id": session_id,
                "user_message_id": str(user_message_id),
                "assistant_message_id": str(assistant_message_id),
                "response_time_ms": response_time_ms
            }
            
        except Exception as e:
            # Store error message
            error_message_id = ObjectId()
            error_message_doc = {
                "_id": error_message_id,
                "content": f"Error: {str(e)}",
                "message_type": MessageType.SYSTEM,
                "session_id": session_id,
                "user_id": user_id,
                "timestamp": datetime.now(),
                "metadata": {"error": True}
            }
            
            self.message_service.collection.insert_one(error_message_doc)
            
            return {
                "response": "I'm sorry, I encountered an error processing your request.",
                "session_id": session_id,
                "error": str(e),
                "error_message_id": str(error_message_id)
            }
    
    def get_chat_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get chat history for a session"""
        messages = self.message_service.collection.find(
            {"session_id": session_id}
        ).sort("timestamp", 1).limit(limit)
        
        history = []
        for msg in messages:
            history.append({
                "id": str(msg["_id"]),
                "content": msg["content"],
                "type": msg["message_type"],
                "timestamp": msg["timestamp"].isoformat(),
                "metadata": msg.get("metadata", {})
            })
        
        return history


# Global instance
_simple_chat_service = None

def get_simple_chat_service() -> SimpleChatService:
    """Get or create simple chat service instance"""
    global _simple_chat_service
    if _simple_chat_service is None:
        _simple_chat_service = SimpleChatService()
    return _simple_chat_service
