"""
Enhanced chat service with sentiment analysis and language detection
"""
import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# <PERSON><PERSON>hain and LangGraph imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.prebuilt import create_react_agent

from langchain_core.messages import HumanMessage

# Qdrant imports for search functionality
from qdrant_client import QdrantClient
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import ChatPromptTemplate

# Local imports
from src.models.message import MessageService, MessageCreate, MessageType
from src.models.booking import BookingService
from src.models.conversation import ConversationService
from src.services.global_retriever import get_global_retriever
from src.services.memory_service import get_memory_manager, get_user_session_id
from src.config.database import COLLECTIONS
import asyncio

# Load environment variables
load_dotenv()

class EnhancedChatService:
    """Enhanced chat service with analytics and database integration"""
    
    def __init__(self):
        self.setup_llm()
        self.setup_global_retriever()
        self.setup_database_services()
        self.memory_manager = None  # Will be initialized async when needed
        self.setup_agent()
    
    def setup_llm(self):
        """Setup Gemini model"""
        self.model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
    
    def setup_global_retriever(self):
        """Setup global retriever service"""
        self.global_retriever = get_global_retriever()
        self.qa_chain = self.global_retriever.get_qa_chain()
        self.retriever = self.global_retriever.get_retriever()
        print("✅ Using Global Retriever Service")
    
    def setup_database_services(self):
        """Setup database services"""
        from src.config.database import get_collection_sync
        # Use synchronous database access for legacy services
        self.message_service = MessageService(get_collection_sync(COLLECTIONS['messages']))
        self.booking_service = BookingService(
            get_collection_sync(COLLECTIONS['bookings']),
            get_collection_sync(COLLECTIONS['time_slots'])
        )
        # Store messages collection for memory manager
        self.messages_collection = get_collection_sync(COLLECTIONS['messages'])

    async def setup_memory_manager(self):
        """Setup modern user-wise memory manager"""
        self.memory_manager = await get_memory_manager()
        print("✅ User Memory Manager initialized")
    
    def setup_agent(self):
        """Setup LangGraph agent with tools"""
        from src.tools.enhanced_tools import get_enhanced_tools
        
        # Get enhanced tools with database integration
        self.tools = get_enhanced_tools(self.qa_chain, self.booking_service)
        
        # Create system prompt
        self.system_prompt = """
You are a professional AI assistant for a customer service center. You MUST use the available tools to help customers.
Never admit ignorance if the `search_database` tool returns a response similar to “information not found”.

Personality:
Use Tony Robbins–style energy and motivational storytelling for booking, cost concerns, shyness or hesitation. For factual/procedural queries, stay factual, concise, and use first-person active language (“you should,” “we recommend”).

"You may not need to use tools for every query - the user may just want to chat!"

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both.

AVAILABLE TOOLS:
1. **search_database**: Use this tool for ANY question or queries related or unrelated to products, services, apps, or information
2. **get_current_date**: Use this tool to get current date and time (MANDATORY for booking processes)
3. **get_available_slots**: Use this tool to show available appointment slots
4. **book_appointment**: Use this tool to schedule appointments

CRITICAL RULES:
- MANDATORY: Use search_database tool for ANY question that needs information (apps, troubleshooting, products, services, "how to", etc.)
- NEVER answer informational questions without using search_database tool first
- Questions like "my app is not working", "how to download", "what is", "I need help with" → MUST use search_database
- For booking: ALWAYS get current date first, then show available slots, then book
- Use get_current_date tool when user asks about dates or for booking processes
- Use get_available_slots when user asks "any available slot", "when can I book", etc.
- Use relevant keywords from user's question for search
- Handle Nepali, Romanized Nepali, and English input
- Respond in clear English or Romanized Nepali
- Do NOT show tool calls in response - only show the final clean answer
- Only simple greetings like "Hello" don't need tools

BOOKING PROCESS:
1. Search for service/course info if needed
2. Get current date (MANDATORY)
3. Show available slots
4. Collect customer details
5. Book appointment

EXAMPLES OF MANDATORY TOOL USAGE:
- "my app is not working" → search_database("app not working troubleshooting")
- "any available slot" → get_available_slots()
- "what's today's date" → get_current_date()
- "book Loksewa" → search_database("Loksewa") → get_current_date() → get_available_slots()

IMPORTANT:
- ALWAYS search first, then provide clean response without showing tool usage
- Response should be helpful and in user's preferred language style
"""
        
        # Memory manager will be initialized async when needed
        self.memory_manager = None
        self.checkpointer = None

        # Create the agentic chatbot (checkpointer will be set later)
        self.agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=self.system_prompt,
            checkpointer=None  # Will be set after async initialization
        )

    async def ensure_initialized(self):
        """Ensure memory manager and checkpointer are initialized"""
        if not self.memory_manager:
            await self.setup_memory_manager()

        if not self.checkpointer and self.memory_manager:
            self.checkpointer = self.memory_manager.checkpointer
            # Recreate agent with proper checkpointer
            self.agent = create_react_agent(
                model=self.model,
                tools=self.tools,
                prompt=self.system_prompt,
                checkpointer=self.checkpointer
            )
            print("✅ Agent updated with persistent checkpointer")
    
    async def process_message(
        self,
        content: str,
        session_id: str,
        user_id: str  # Now required for user-specific memory
    ) -> Dict[str, Any]:
        """Process user message with user-specific memory and analytics"""
        start_time = datetime.now()

        try:
            # Ensure everything is properly initialized
            await self.ensure_initialized()

            # Get or create conversation for this user and session
            conversation = await self.memory_manager.get_conversation_by_session(session_id)
            if not conversation:
                conversation = await self.memory_manager.create_conversation(
                    user_id=user_id,
                    session_id=session_id,
                    initial_message=content
                )

            # Note: conversation_history is available via memory_manager if needed

            # Get recent conversation context (last 10 messages)
            recent_messages = await self.memory_manager.get_conversation_context(
                user_id=user_id,
                conversation_id=str(conversation.id),
                context_type="recent",
                max_messages=4
            )

            # Store user message with analytics and conversation reference
            user_message = await self.message_service.create_message(
                MessageCreate(
                    content=content,
                    message_type=MessageType.USER,
                    session_id=session_id,
                    user_id=user_id,
                    conversation_id=str(conversation.id)
                )
            )

            # Create user session ID for LangGraph
            langgraph_session_id = get_user_session_id(user_id, str(conversation.id))

            # Process with agent using user-specific session
            config = {"configurable": {"thread_id": langgraph_session_id}}

            # Add conversation context to the message
            context_messages = recent_messages + [HumanMessage(content=content)]

            response = self.agent.invoke(
                {"messages": context_messages},
                config=config
            )

            # Extract assistant response
            assistant_content = response["messages"][-1].content

            # Calculate response time
            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Store assistant message with analytics and conversation reference
            assistant_message = await self.message_service.create_message(
                MessageCreate(
                    content=assistant_content,
                    message_type=MessageType.ASSISTANT,
                    session_id=session_id,
                    user_id=user_id,
                    conversation_id=str(conversation.id)
                ),
                response_time_ms=response_time_ms
            )

            # Update conversation metrics
            await self.memory_manager.update_conversation_metrics(session_id)

            return {
                "response": assistant_content,
                "session_id": session_id,
                "conversation_id": str(conversation.id),
                "user_message_id": str(user_message.id),
                "assistant_message_id": str(assistant_message.id),
                "response_time_ms": response_time_ms,
                "user_analytics": user_message.analytics.model_dump() if user_message.analytics else None,
                "assistant_analytics": assistant_message.analytics.model_dump() if assistant_message.analytics else None
            }
            
        except Exception as e:
            # Store error message
            error_message = await self.message_service.create_message(
                MessageCreate(
                    content=f"Error processing message: {str(e)}",
                    message_type=MessageType.SYSTEM,
                    session_id=session_id,
                    user_id=user_id
                )
            )
            
            return {
                "response": "I apologize, but I encountered an error processing your message. Please try again.",
                "session_id": session_id,
                "error": str(e),
                "error_message_id": str(error_message.id)
            }
    
    def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation history with analytics"""
        messages = self.message_service.get_messages_by_session(session_id, limit)
        
        return [
            {
                "id": str(msg.id),
                "content": msg.content,
                "type": msg.message_type,
                "timestamp": msg.timestamp.isoformat(),
                "analytics": msg.analytics.model_dump() if msg.analytics else None
            }
            for msg in messages
        ]
    
    def get_user_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get analytics summary for a user"""
        messages = self.message_service.get_user_messages(user_id)
        
        if not messages:
            return {"total_messages": 0}
        
        # Calculate analytics
        total_messages = len(messages)
        user_messages = [msg for msg in messages if msg.message_type == MessageType.USER]
        
        if not user_messages:
            return {"total_messages": total_messages, "user_messages": 0}
        
        # Sentiment analysis
        sentiments = [msg.analytics.sentiment for msg in user_messages if msg.analytics]
        sentiment_scores = [msg.analytics.sentiment_score for msg in user_messages if msg.analytics]
        
        # Language analysis
        languages = [msg.analytics.language for msg in user_messages if msg.analytics]
        
        # Intent analysis
        booking_intents = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_booking_intent)
        complaints = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_complaint)
        
        return {
            "total_messages": total_messages,
            "user_messages": len(user_messages),
            "sentiment_distribution": {
                "positive": sentiments.count("positive"),
                "negative": sentiments.count("negative"),
                "neutral": sentiments.count("neutral")
            },
            "average_sentiment_score": sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0,
            "languages_used": list(set(languages)),
            "booking_intents": booking_intents,
            "complaints": complaints,
            "last_activity": messages[0].timestamp.isoformat() if messages else None
        }

# Global chat service instance
chat_service = None

def get_chat_service() -> EnhancedChatService:
    """Get or create chat service instance"""
    global chat_service
    if chat_service is None:
        chat_service = EnhancedChatService()
    return chat_service
