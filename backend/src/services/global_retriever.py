"""
Global Retriever Service - Production-level singleton retriever
"""
import os
from typing import Optional
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from langchain_qdrant.qdrant import RetrievalMode
from langchain.chains import RetrievalQ<PERSON>
from langchain_openai import ChatOpenAI


class GlobalRetrieverService:
    """Singleton service for global retriever management"""
    
    _instance: Optional['GlobalRetrieverService'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_retriever()
            GlobalRetrieverService._initialized = True
    
    def _setup_retriever(self):
        """Initialize the global retriever components"""
        try:
            print("🔧 Initializing Global Retriever Service...")
            
            # Initialize OpenAI embeddings
            self.embeddings = OpenAIEmbeddings(
    model="text-embedding-3-large",
    api_key=os.getenv("OPENAI_API_KEY"),
    dimensions=1536
)
            
            # Initialize Qdrant client
            self.qdrant_client = QdrantClient(
                host=os.getenv("QDRANT_HOST", "*************"),
                port=int(os.getenv("QDRANT_PORT", "6333")),
            )
            
            # Setup vector store
            self.vector_store = QdrantVectorStore(
                client=self.qdrant_client,
                collection_name=os.getenv("QDRANT_COLLECTION", "langsmit_test"),
                embedding=self.embeddings,
                retrieval_mode=RetrievalMode.DENSE,
                content_payload_key="page_content",
                metadata_payload_key="metadata"
            )
            
            # Create retriever
            self.retriever = self.vector_store.as_retriever(
                search_type="similarity",
                search_kwargs={"k": 5}
            )
            
            # Initialize LLM for QA chain
            self.llm = ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0.1,
                api_key=os.getenv("OPENAI_API_KEY")
            )
            
            # Create QA chain
            self.qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type="stuff",
                retriever=self.retriever,
                return_source_documents=True,
                verbose=False
            )
            
            print("✅ Global Retriever Service initialized successfully")
            
        except Exception as e:
            print(f"❌ Error initializing Global Retriever Service: {e}")
            raise
    
    def get_retriever(self):
        """Get the global retriever instance"""
        return self.retriever
    
    def get_qa_chain(self):
        """Get the global QA chain instance"""
        return self.qa_chain
    
    def get_vector_store(self):
        """Get the global vector store instance"""
        return self.vector_store
    
    def search(self, query: str, k: int = 5):
        """Search using the global retriever"""
        try:
            docs = self.retriever.get_relevant_documents(query)
            return docs[:k]
        except Exception as e:
            print(f"Error in global retriever search: {e}")
            return []
    
    def qa_search(self, query: str):
        """Perform QA search using the global QA chain"""
        try:
            result = self.qa_chain.invoke({"query": query})
            return result
        except Exception as e:
            print(f"Error in global QA search: {e}")
            return {"result": f"Error: {str(e)}", "source_documents": []}


# Global instance (lazy initialization)
global_retriever = None


def get_global_retriever():
    """Get the global retriever service instance with lazy initialization"""
    global global_retriever
    if global_retriever is None:
        try:
            global_retriever = GlobalRetrieverService()
        except Exception as e:
            print(f"❌ Failed to initialize Global Retriever Service: {e}")
            # Return a dummy retriever that doesn't crash the app
            global_retriever = DummyRetrieverService()
    return global_retriever


class DummyRetrieverService:
    """Dummy retriever service for when Qdrant is not available"""

    def search(self, query: str, k: int = 5):
        """Dummy search that returns empty results"""
        return []

    def qa_search(self, query: str):
        """Dummy QA search that returns a simple response"""
        return {
            "result": "I apologize, but the knowledge base is currently unavailable. Please try again later.",
            "source_documents": []
        }

    def get_qa_chain(self):
        """Return a dummy QA chain"""
        return DummyQAChain()


class DummyQAChain:
    """Dummy QA chain for when Qdrant is not available"""

    def invoke(self, inputs):
        """Return a dummy response"""
        return {
            "result": "I apologize, but the knowledge base is currently unavailable. Please try again later.",
            "source_documents": []
        }

    def get_qa_chain(self):
        """Return a dummy QA chain"""
        return DummyQAChain()


class DummyQAChain:
    """Dummy QA chain for when Qdrant is not available"""

    def invoke(self, inputs):
        """Return a dummy response"""
        return {
            "result": "I apologize, but the knowledge base is currently unavailable. Please try again later.",
            "source_documents": []
        }
