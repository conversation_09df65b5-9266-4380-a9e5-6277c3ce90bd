"""
Chat routes for enhanced chat functionality
"""
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status, Request
from pydantic import BaseModel

from src.services.chat_service import get_chat_service
from src.services.memory_service import get_memory_manager
from src.routes.auth import get_current_user

router = APIRouter()

# Request/Response models
class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    session_id: str
    conversation_id: Optional[str] = None
    user_message_id: str
    assistant_message_id: str
    response_time_ms: int
    user_analytics: Optional[Dict[str, Any]] = None
    assistant_analytics: Optional[Dict[str, Any]] = None

class ChatMessage(BaseModel):
    content: str
    message_type: str
    timestamp: str
    analytics: Optional[Dict[str, Any]] = None

class ConversationHistoryResponse(BaseModel):
    session_id: str
    messages: List[Dict[str, Any]]
    total_messages: int

class UserAnalyticsResponse(BaseModel):
    user_id: str
    analytics: Dict[str, Any]

class SessionListResponse(BaseModel):
    sessions: List[Dict[str, str]]
    total: int

class ConversationListResponse(BaseModel):
    conversations: List[Dict[str, Any]]
    total: int

class ConversationCreateRequest(BaseModel):
    title: Optional[str] = None
    initial_message: Optional[str] = None

class ConversationUpdateRequest(BaseModel):
    title: Optional[str] = None
    status: Optional[str] = None

class ChatInConversationRequest(BaseModel):
    message: str
    session_id: str

# Routes
@router.post("/", response_model=ChatResponse)
async def chat_endpoint(chat_request: ChatRequest, request: Request, current_user = Depends(get_current_user)):
    """Main chat endpoint - simplified to only take message"""
    try:
        # Check if user can send message
        if not await current_user.can_send_message():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Daily message limit reached ({current_user.max_messages_per_day} messages per day)"
            )

        # Get or create active conversation for user
        memory_manager = await get_memory_manager()

        # Get user's most recent active conversation
        conversations = await memory_manager.get_user_conversations(str(current_user.id), limit=1)

        if conversations and conversations[0].status == "active":
            # Use existing active conversation
            session_id = conversations[0].session_id
        else:
            # Create new conversation
            session_id = str(uuid.uuid4())
            await memory_manager.create_conversation(
                user_id=str(current_user.id),
                session_id=session_id,
                initial_message=chat_request.message
            )

        # Get chat service
        chat_service = get_chat_service()

        # Process message with authenticated user
        result = await chat_service.process_message(
            content=chat_request.message,
            session_id=session_id,
            user_id=str(current_user.id)
        )

        if "error" in result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )

        return ChatResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat processing failed: {str(e)}"
        )

@router.post("/conversation", response_model=ChatResponse)
async def chat_in_conversation(chat_request: ChatInConversationRequest, request: Request, current_user = Depends(get_current_user)):
    """Send message in a specific conversation"""
    try:
        # Check if user can send message
        if not await current_user.can_send_message():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Daily message limit reached ({current_user.max_messages_per_day} messages per day)"
            )

        # Verify user has access to this conversation
        memory_manager = await get_memory_manager()
        conversation = await memory_manager.get_conversation_by_session(chat_request.session_id)

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        # Check if user has access to this conversation
        user_has_access = any(
            p.user_id == str(current_user.id) for p in conversation.participants
        )

        if not user_has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this conversation"
            )

        # Get chat service
        chat_service = get_chat_service()

        # Process message with authenticated user
        result = await chat_service.process_message(
            content=chat_request.message,
            session_id=chat_request.session_id,
            user_id=str(current_user.id)
        )

        if "error" in result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )

        return ChatResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat processing failed: {str(e)}"
        )

@router.get("/sessions", response_model=SessionListResponse)
async def get_user_sessions(current_user = Depends(get_current_user)):
    """Get all chat sessions for the current user"""
    try:
        chat_service = get_chat_service()
        
        # Get user messages and extract unique session IDs
        messages = chat_service.message_service.get_user_messages(str(current_user.id), 1000)
        
        # Group by session and get session info
        sessions = {}
        for message in messages:
            session_id = message.session_id
            if session_id not in sessions:
                sessions[session_id] = {
                    "session_id": session_id,
                    "last_activity": message.timestamp.isoformat(),
                    "message_count": 0
                }
            sessions[session_id]["message_count"] += 1
            
            # Update last activity if this message is more recent
            if message.timestamp.isoformat() > sessions[session_id]["last_activity"]:
                sessions[session_id]["last_activity"] = message.timestamp.isoformat()
        
        session_list = list(sessions.values())
        session_list.sort(key=lambda x: x["last_activity"], reverse=True)
        
        return SessionListResponse(
            sessions=session_list,
            total=len(session_list)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user sessions: {str(e)}"
        )

@router.get("/history/{session_id}", response_model=ConversationHistoryResponse)
async def get_session_history(session_id: str, limit: int = 50, current_user = Depends(get_current_user)):
    """Get conversation history for a specific session"""
    try:
        chat_service = get_chat_service()
        history = chat_service.get_conversation_history(session_id, limit)

        return ConversationHistoryResponse(
            session_id=session_id,
            messages=history,
            total_messages=len(history)
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve conversation history: {str(e)}"
        )

@router.get("/analytics/user", response_model=UserAnalyticsResponse)
async def get_current_user_analytics(current_user = Depends(get_current_user)):
    """Get analytics for the current user"""
    try:
        chat_service = get_chat_service()
        analytics = chat_service.get_user_analytics(str(current_user.id))
        
        return UserAnalyticsResponse(
            user_id=str(current_user.id),
            analytics=analytics
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user analytics: {str(e)}"
        )

@router.get("/analytics/session/{session_id}")
async def get_session_analytics(session_id: str):
    """Get analytics for a specific session"""
    try:
        chat_service = get_chat_service()
        messages = chat_service.message_service.get_messages_by_session(session_id)
        
        if not messages:
            return {
                "session_id": session_id,
                "analytics": {"total_messages": 0}
            }
        
        # Calculate session analytics
        user_messages = [msg for msg in messages if msg.message_type == "user"]
        assistant_messages = [msg for msg in messages if msg.message_type == "assistant"]
        
        # Sentiment analysis
        sentiments = [msg.analytics.sentiment for msg in user_messages if msg.analytics]
        sentiment_scores = [msg.analytics.sentiment_score for msg in user_messages if msg.analytics]
        
        # Language analysis
        languages = [msg.analytics.language for msg in user_messages if msg.analytics]
        
        # Intent analysis
        booking_intents = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_booking_intent)
        complaints = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_complaint)
        
        # Response times
        response_times = [msg.analytics.response_time_ms for msg in assistant_messages if msg.analytics and msg.analytics.response_time_ms]
        
        analytics = {
            "session_id": session_id,
            "total_messages": len(messages),
            "user_messages": len(user_messages),
            "assistant_messages": len(assistant_messages),
            "sentiment_distribution": {
                "positive": sentiments.count("positive"),
                "negative": sentiments.count("negative"),
                "neutral": sentiments.count("neutral")
            },
            "average_sentiment_score": sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0,
            "languages_used": list(set(languages)),
            "booking_intents": booking_intents,
            "complaints": complaints,
            "average_response_time_ms": sum(response_times) / len(response_times) if response_times else 0,
            "session_duration": {
                "start": messages[0].timestamp.isoformat() if messages else None,
                "end": messages[-1].timestamp.isoformat() if messages else None
            }
        }
        
        return {
            "session_id": session_id,
            "analytics": analytics
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve session analytics: {str(e)}"
        )

@router.delete("/session/{session_id}")
async def delete_session(session_id: str, current_user = Depends(get_current_user)):
    """Delete a chat session and all its messages"""
    try:
        chat_service = get_chat_service()
        
        # Delete all messages in the session for the current user
        result = chat_service.message_service.collection.delete_many({
            "session_id": session_id,
            "user_id": str(current_user.id)
        })
        
        return {
            "message": f"Session {session_id} deleted successfully",
            "deleted_messages": result.deleted_count
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete session: {str(e)}"
        )

@router.get("/search")
async def search_messages(
    query: str,
    session_id: Optional[str] = None,
    current_user = Depends(get_current_user)
):
    """Search messages by content"""
    try:
        chat_service = get_chat_service()
        
        # Build search filter
        search_filter = {
            "user_id": str(current_user.id),
            "content": {"$regex": query, "$options": "i"}
        }
        
        if session_id:
            search_filter["session_id"] = session_id
        
        # Search messages
        messages = list(chat_service.message_service.collection.find(
            search_filter
        ).sort("timestamp", -1).limit(50))
        
        # Format results
        results = []
        for msg in messages:
            results.append({
                "id": str(msg["_id"]),
                "content": msg["content"],
                "message_type": msg["message_type"],
                "session_id": msg["session_id"],
                "timestamp": msg["timestamp"].isoformat(),
                "analytics": msg.get("analytics")
            })
        
        return {
            "query": query,
            "session_id": session_id,
            "results": results,
            "total": len(results)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search messages: {str(e)}"
        )

# Conversation Management Endpoints

@router.get("/conversations", response_model=ConversationListResponse)
async def get_user_conversations(
    limit: int = 50,
    skip: int = 0,
    current_user = Depends(get_current_user)
):
    """Get all conversations for the current user"""
    try:
        memory_manager = await get_memory_manager()
        conversations = await memory_manager.get_user_conversations(
            str(current_user.id), limit, skip
        )

        conversation_list = []
        for conv in conversations:
            # Get preview of last message
            conv_with_preview = await memory_manager.conversation_service.get_conversation_with_preview(
                conv.session_id
            )

            conversation_list.append({
                "id": str(conv.id),
                "session_id": conv.session_id,
                "title": conv.title,
                "conversation_type": conv.conversation_type,
                "status": conv.status,
                "created_at": conv.created_at.isoformat(),
                "updated_at": conv.updated_at.isoformat(),
                "last_message_at": conv.last_message_at.isoformat() if conv.last_message_at else None,
                "metrics": conv.metrics.model_dump(),
                "preview_message": conv_with_preview.preview_message if conv_with_preview else None
            })

        return ConversationListResponse(
            conversations=conversation_list,
            total=len(conversation_list)
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve conversations: {str(e)}"
        )

@router.post("/conversations")
async def create_conversation(
    request: ConversationCreateRequest,
    current_user = Depends(get_current_user)
):
    """Create a new conversation"""
    try:
        memory_manager = await get_memory_manager()

        # Generate session ID
        session_id = str(uuid.uuid4())

        conversation = await memory_manager.create_conversation(
            user_id=str(current_user.id),
            session_id=session_id,
            initial_message=request.initial_message
        )

        return {
            "id": str(conversation.id),
            "session_id": conversation.session_id,
            "title": conversation.title,
            "conversation_type": conversation.conversation_type,
            "status": conversation.status,
            "created_at": conversation.created_at.isoformat(),
            "message": "Conversation created successfully"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create conversation: {str(e)}"
        )

@router.get("/conversations/{conversation_id}")
async def get_conversation_details(
    conversation_id: str,
    current_user = Depends(get_current_user)
):
    """Get detailed information about a specific conversation"""
    try:
        memory_manager = await get_memory_manager()

        # Get conversation by ID (assuming conversation_id is session_id)
        conversation = await memory_manager.get_conversation_by_session(conversation_id)

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        # Check if user has access to this conversation
        user_has_access = any(
            p.user_id == str(current_user.id) for p in conversation.participants
        )

        if not user_has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this conversation"
            )

        return {
            "id": str(conversation.id),
            "session_id": conversation.session_id,
            "title": conversation.title,
            "conversation_type": conversation.conversation_type,
            "status": conversation.status,
            "participants": [p.model_dump() for p in conversation.participants],
            "created_at": conversation.created_at.isoformat(),
            "updated_at": conversation.updated_at.isoformat(),
            "last_message_at": conversation.last_message_at.isoformat() if conversation.last_message_at else None,
            "metrics": conversation.metrics.model_dump(),
            "metadata": conversation.metadata
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve conversation: {str(e)}"
        )

@router.put("/conversations/{conversation_id}")
async def update_conversation(
    conversation_id: str,
    request: ConversationUpdateRequest,
    current_user = Depends(get_current_user)
):
    """Update conversation details"""
    try:
        memory_manager = await get_memory_manager()

        # Get conversation to check access
        conversation = await memory_manager.get_conversation_by_session(conversation_id)

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        # Check if user has access to this conversation
        user_has_access = any(
            p.user_id == str(current_user.id) for p in conversation.participants
        )

        if not user_has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this conversation"
            )

        # Update conversation
        from src.models.conversation import ConversationUpdate
        update_data = ConversationUpdate(
            title=request.title,
            status=request.status
        )

        updated_conversation = await memory_manager.conversation_service.update_conversation(
            conversation_id, update_data
        )

        if not updated_conversation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update conversation"
            )

        return {
            "id": str(updated_conversation.id),
            "session_id": updated_conversation.session_id,
            "title": updated_conversation.title,
            "status": updated_conversation.status,
            "updated_at": updated_conversation.updated_at.isoformat(),
            "message": "Conversation updated successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update conversation: {str(e)}"
        )

@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    current_user = Depends(get_current_user)
):
    """Delete (archive) a conversation"""
    try:
        memory_manager = await get_memory_manager()

        # Get conversation to check access
        conversation = await memory_manager.get_conversation_by_session(conversation_id)

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        # Check if user has access to this conversation
        user_has_access = any(
            p.user_id == str(current_user.id) for p in conversation.participants
        )

        if not user_has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this conversation"
            )

        # Soft delete conversation
        success = await memory_manager.conversation_service.delete_conversation(conversation_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to delete conversation"
            )

        return {
            "message": "Conversation deleted successfully",
            "conversation_id": conversation_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete conversation: {str(e)}"
        )
