"""
Message model for the chat system with sentiment analysis and language detection
"""
from datetime import datetime,timezone
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from bson import ObjectId
from enum import Enum

from pydantic import GetJsonSchemaHandler
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema
from typing import Any

class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate),
                ])
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            ),
        )

    @classmethod
    def validate(cls, v):
        if isinstance(v, ObjectId):
            return v
        if isinstance(v, str) and ObjectId.is_valid(v):
            return ObjectId(v)
        raise ValueError("Invalid ObjectId")

    @classmethod
    def __get_pydantic_json_schema__(
        cls, schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        return {"type": "string"}

class MessageType(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class SentimentType(str, Enum):
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"

class MessageAnalytics(BaseModel):
    """Analytics data for each message"""
    sentiment: SentimentType
    sentiment_score: float = Field(..., ge=-1.0, le=1.0)
    language: str
    language_confidence: float = Field(..., ge=0.0, le=1.0)
    word_count: int
    character_count: int
    contains_booking_intent: bool = False
    contains_complaint: bool = False
    response_time_ms: Optional[int] = None

class MessageBase(BaseModel):
    """Base message model"""
    content: str = Field(..., min_length=1, max_length=5000)
    message_type: MessageType
    session_id: str

class MessageCreate(MessageBase):
    """Message creation model"""
    user_id: Optional[str] = None
    reply_to_message_id: Optional[str] = None  # ID of message this is replying to
    conversation_id: Optional[str] = None  # Reference to conversation

class MessageInDB(MessageBase):
    """Message model as stored in database"""
    id: PyObjectId = Field(alias="_id")
    user_id: Optional[str] = None
    reply_to_message_id: Optional[str] = None  # ID of message this is replying to
    conversation_id: Optional[str] = None  # Reference to conversation
    timestamp: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    analytics: Optional[MessageAnalytics] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

    # Threading support
    thread_id: Optional[str] = None  # For message threading
    is_thread_root: bool = False  # True if this message starts a thread
    reply_count: int = 0  # Number of direct replies to this message

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class MessageResponse(MessageBase):
    """Message response model"""
    id: str
    user_id: Optional[str] = None
    reply_to_message_id: Optional[str] = None
    conversation_id: Optional[str] = None
    timestamp: datetime
    analytics: Optional[MessageAnalytics] = None
    thread_id: Optional[str] = None
    is_thread_root: bool = False
    reply_count: int = 0

    # Additional fields for enhanced response
    replied_message: Optional[Dict[str, Any]] = None  # The message this is replying to
    replies: Optional[List[Dict[str, Any]]] = None  # Direct replies to this message

class MessageService:
    """Message service for database operations and analytics"""
    
    def __init__(self, collection):
        self.collection = collection
    
    def analyze_sentiment(self, text: str) -> tuple[SentimentType, float]:
        """Analyze sentiment using TextBlob"""
        try:
            from textblob import TextBlob
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            
            if polarity > 0.1:
                sentiment = SentimentType.POSITIVE
            elif polarity < -0.1:
                sentiment = SentimentType.NEGATIVE
            else:
                sentiment = SentimentType.NEUTRAL
                
            return sentiment, polarity
        except Exception:
            return SentimentType.NEUTRAL, 0.0
    
    def detect_language(self, text: str) -> tuple[str, float]:
        """Detect language using langdetect"""
        try:
            from langdetect import detect, detect_langs
            
            # Get primary language
            language = detect(text)
            
            # Get confidence score
            lang_probs = detect_langs(text)
            confidence = max([prob.prob for prob in lang_probs if prob.lang == language], default=0.0)
            
            return language, confidence
        except Exception:
            return "unknown", 0.0
    
    def analyze_intent(self, text: str) -> Dict[str, bool]:
        """Analyze message for specific intents"""
        text_lower = text.lower()
        
        # Booking intent keywords
        booking_keywords = [
            'book', 'appointment', 'schedule', 'reserve', 'slot',
            'available', 'time', 'date', 'meeting'
        ]
        
        # Complaint keywords
        complaint_keywords = [
            'problem', 'issue', 'error', 'not working', 'broken',
            'complaint', 'frustrated', 'angry', 'disappointed'
        ]
        
        contains_booking = any(keyword in text_lower for keyword in booking_keywords)
        contains_complaint = any(keyword in text_lower for keyword in complaint_keywords)
        
        return {
            "contains_booking_intent": contains_booking,
            "contains_complaint": contains_complaint
        }
    
    def create_analytics(self, content: str, response_time_ms: Optional[int] = None) -> MessageAnalytics:
        """Create analytics for a message"""
        sentiment, sentiment_score = self.analyze_sentiment(content)
        language, language_confidence = self.detect_language(content)
        intent_analysis = self.analyze_intent(content)
        
        return MessageAnalytics(
            sentiment=sentiment,
            sentiment_score=sentiment_score,
            language=language,
            language_confidence=language_confidence,
            word_count=len(content.split()),
            character_count=len(content),
            contains_booking_intent=intent_analysis["contains_booking_intent"],
            contains_complaint=intent_analysis["contains_complaint"],
            response_time_ms=response_time_ms
        )
    
    async def create_message(self, message_data: MessageCreate, response_time_ms: Optional[int] = None) -> MessageInDB:
        """Create new message with analytics and reply tracking"""
        # Generate analytics
        analytics = self.create_analytics(message_data.content, response_time_ms)

        # Handle reply tracking
        thread_id = None
        is_thread_root = False

        if message_data.reply_to_message_id:
            # This is a reply to another message
            try:
                replied_message = await self.collection.find_one({"_id": ObjectId(message_data.reply_to_message_id)})
            except TypeError:
                replied_message = self.collection.find_one({"_id": ObjectId(message_data.reply_to_message_id)})

            if replied_message:
                # Use existing thread_id or create new one
                thread_id = replied_message.get("thread_id") or str(replied_message["_id"])

                # Update reply count for the original message
                try:
                    await self.collection.update_one(
                        {"_id": ObjectId(message_data.reply_to_message_id)},
                        {"$inc": {"reply_count": 1}}
                    )
                except TypeError:
                    self.collection.update_one(
                        {"_id": ObjectId(message_data.reply_to_message_id)},
                        {"$inc": {"reply_count": 1}}
                    )

                # If the replied message doesn't have a thread_id, set it as thread root
                if not replied_message.get("thread_id"):
                    try:
                        await self.collection.update_one(
                            {"_id": ObjectId(message_data.reply_to_message_id)},
                            {"$set": {"thread_id": str(replied_message["_id"]), "is_thread_root": True}}
                        )
                    except TypeError:
                        self.collection.update_one(
                            {"_id": ObjectId(message_data.reply_to_message_id)},
                            {"$set": {"thread_id": str(replied_message["_id"]), "is_thread_root": True}}
                        )

        # Create a unique ObjectId for this message
        message_id = ObjectId()

        # Create message document with explicit _id
        message_doc = {
            "_id": message_id,
            "content": message_data.content,
            "message_type": message_data.message_type,
            "session_id": message_data.session_id,
            "user_id": message_data.user_id,
            "reply_to_message_id": message_data.reply_to_message_id,
            "conversation_id": message_data.conversation_id,
            "timestamp": datetime.now(timezone.utc),
            "analytics": analytics.model_dump(),
            "metadata": {},
            "thread_id": thread_id,
            "is_thread_root": is_thread_root,
            "reply_count": 0
        }

        # Handle both sync and async collections
        try:
            # Try async first
            await self.collection.insert_one(message_doc)
        except TypeError:
            print("")
            # If await fails, it's a sync collection
            # self.collection.insert_one(message_doc)

        return MessageInDB(**message_doc)
    
    def get_messages_by_session(self, session_id: str, limit: int = 50) -> List[MessageInDB]:
        """Get messages by session ID"""
        messages = self.collection.find(
            {"session_id": session_id}
        ).sort("timestamp", 1).limit(limit)
        
        return [MessageInDB(**msg) for msg in messages]
    
    def get_user_messages(self, user_id: str, limit: int = 100) -> List[MessageInDB]:
        """Get messages by user ID"""
        messages = self.collection.find(
            {"user_id": user_id}
        ).sort("timestamp", -1).limit(limit)

        return [MessageInDB(**msg) for msg in messages]

    async def get_conversation_messages(self, conversation_id: str, limit: int = 50) -> List[MessageInDB]:
        """Get messages by conversation ID"""
        messages = self.collection.find(
            {"conversation_id": conversation_id}
        ).sort("timestamp", 1).limit(limit)

        # Handle both sync and async collections
        try:
            return [MessageInDB(**msg) async for msg in messages]
        except TypeError:
            # For sync collections, use regular iteration
            return [MessageInDB(**msg) for msg in messages]
    
    def get_analytics_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get analytics summary for a date range"""
        pipeline = [
            {
                "$match": {
                    "timestamp": {"$gte": start_date, "$lte": end_date}
                }
            },
            {
                "$group": {
                    "_id": None,
                    "total_messages": {"$sum": 1},
                    "avg_sentiment_score": {"$avg": "$analytics.sentiment_score"},
                    "positive_messages": {
                        "$sum": {"$cond": [{"$eq": ["$analytics.sentiment", "positive"]}, 1, 0]}
                    },
                    "negative_messages": {
                        "$sum": {"$cond": [{"$eq": ["$analytics.sentiment", "negative"]}, 1, 0]}
                    },
                    "neutral_messages": {
                        "$sum": {"$cond": [{"$eq": ["$analytics.sentiment", "neutral"]}, 1, 0]}
                    },
                    "booking_intents": {
                        "$sum": {"$cond": ["$analytics.contains_booking_intent", 1, 0]}
                    },
                    "complaints": {
                        "$sum": {"$cond": ["$analytics.contains_complaint", 1, 0]}
                    },
                    "languages": {"$addToSet": "$analytics.language"}
                }
            }
        ]
        
        result = list(self.collection.aggregate(pipeline))
        return result[0] if result else {}
