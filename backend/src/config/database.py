"""
Database configuration and connection setup for MongoDB
"""
import os
from pymongo import MongoClient, AsyncMongoClient
from pymongo.errors import ConnectionFailure
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db():
    """Get sync database instance"""
    try:
        mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        database_name = os.getenv("DATABASE_NAME", "chatbot_system")
        return MongoClient(mongo_uri)[database_name]
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB (sync): {e}")
        raise Exception("Database connection failed")

def get_async_db():
    """Get async database instance"""
    try:
        mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        database_name = os.getenv("DATABASE_NAME", "chatbot_system")
        return AsyncMongoClient(mongo_uri)[database_name]
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB (async): {e}")
        raise Exception("Async database connection failed")

def get_collection_sync(collection_name):
    """Get sync collection"""
    return get_db()[collection_name]

async def get_collection(collection_name):
    """Get async collection"""
    return get_async_db()[collection_name]

# Collection names
COLLECTIONS = {
    'users': 'users',
    'messages': 'messages',
    'conversations': 'conversations',
    'bookings': 'bookings',
    'time_slots': 'time_slots',
    'analytics': 'analytics'
}

async def initialize_collections():
    """Initialize collections with indexes and constraints"""
    async_db = get_async_db()

    # Users collection indexes
    await async_db[COLLECTIONS['users']].create_index("email", unique=True)
    await async_db[COLLECTIONS['users']].create_index("phone", unique=True)

    # Messages collection indexes
    await async_db[COLLECTIONS['messages']].create_index([("user_id", 1), ("timestamp", -1)])
    await async_db[COLLECTIONS['messages']].create_index("session_id")
    await async_db[COLLECTIONS['messages']].create_index("conversation_id")
    await async_db[COLLECTIONS['messages']].create_index("reply_to_message_id")
    await async_db[COLLECTIONS['messages']].create_index("thread_id")

    # Conversations collection indexes
    await async_db[COLLECTIONS['conversations']].create_index("session_id", unique=True)
    await async_db[COLLECTIONS['conversations']].create_index([("participants.user_id", 1), ("last_message_at", -1)])
    await async_db[COLLECTIONS['conversations']].create_index([("status", 1), ("updated_at", -1)])

    # Bookings collection indexes
    await async_db[COLLECTIONS['bookings']].create_index([("user_id", 1), ("booking_date", -1)])
    await async_db[COLLECTIONS['bookings']].create_index([("date", 1), ("time", 1)], unique=True)

    # Time slots collection indexes
    await async_db[COLLECTIONS['time_slots']].create_index([("date", 1), ("time", 1)], unique=True)

    # Analytics collection indexes
    await async_db[COLLECTIONS['analytics']].create_index([("date", 1), ("metric_type", 1)])

    logger.info("Collections and indexes initialized successfully")

async def create_sample_time_slots():
    """Create sample time slots for booking system"""
    from datetime import datetime, timedelta, timezone

    async_db = get_async_db()
    time_slots_collection = async_db[COLLECTIONS['time_slots']]

    # Clear existing slots
    await time_slots_collection.delete_many({})

    # Create slots for next 30 days
    base_date = datetime.now(timezone.utc).replace(hour=9, minute=0, second=0, microsecond=0)
    slots = []

    for i in range(30):  # Next 30 days
        date = base_date + timedelta(days=i)
        # Skip weekends
        if date.weekday() < 5:  # Monday=0, Friday=4
            for hour in [9, 11, 14, 16]:  # 9AM, 11AM, 2PM, 4PM
                slot_datetime = date.replace(hour=hour)
                slots.append({
                    "date": slot_datetime.strftime("%Y-%m-%d"),
                    "time": slot_datetime.strftime("%H:%M"),
                    "datetime": slot_datetime,
                    "available": True,
                    "max_bookings": 5,  # Allow multiple bookings per slot
                    "current_bookings": 0,
                    "created_at": datetime.now(timezone.utc)
                })

    if slots:
        await time_slots_collection.insert_many(slots)
        logger.info(f"Created {len(slots)} time slots")

async def main():
    """Main function for testing database setup"""
    try:
        await initialize_collections()
        await create_sample_time_slots()
        print("Database setup completed successfully!")
    except Exception as e:
        print(f"Failed to setup database: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
